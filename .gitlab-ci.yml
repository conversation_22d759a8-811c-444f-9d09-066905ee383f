---

variables:
  GOLANG_DOCKER_IMAGE: 'golang:1.17.0-bullseye'
  GOOS: 'linux'
  GOARCH: 'amd64 arm64'
  LinuxBuildType: 'tar'
  MainDir: "."
  PackageNames: "mydbops-sensu-webhook-handler"
  TarBuildDir: "nil"
  CompressTarBuild: 'yes'
  TarBuildWithBaseDir: 'no'
  RpmBuildExtraOptions: ''
  DebBuildExtraOptions: ''
  S3GroupName: "monitoring"

include:
  - project: 'mydb/devops/mydbcicd.dependencies'
    ref: main
    file:
      - 'mydbci_files/golang-build-upload/general.yml'
