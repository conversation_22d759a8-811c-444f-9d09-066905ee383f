# [1.5.0](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/compare/1.4.0...1.5.0) (2025-04-22)


### Bug Fixes

* ci cd file fix ([ecdbc64](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/ecdbc64d807ca4df2d5eda344404a43f11e156c0))
* ci cd file fix ([0bf3033](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/0bf30334661b2d39ad5649ed08cd10ebf1fe98e9))
* ci cd file fix ([5e5aafe](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/5e5aafe48191ac2662bf387a308c57630c9348eb))


### Features

* added entity_alias to support proxy entity names ([be58ab9](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/be58ab9e9cd89bb21e159e04367f20d31e0cb126))

# [1.4.0](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/compare/1.3.0...1.4.0) (2024-11-13)


### Features

* added feature to split the disk check alert if it contains warning and critical in same message ([14dc069](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/14dc0698fd7c08f73543db0f08562eb9effd7ebe))

# [1.3.0](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/compare/1.2.0...1.3.0) (2024-03-26)


### Features

* added support for proxy auth ([9323eef](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/9323eefeed8dd8266287bec7c41ec07e2aefe69c))

# [1.2.0](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/compare/1.1.0...1.2.0) (2023-04-13)


### Features

* status filter added ([38520ea](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/38520ea9e518e005e17f37609ed2535c97a8d14a))

# [1.1.0](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/compare/1.0.0...1.1.0) (2023-04-02)


### Bug Fixes

* icon missed in one condition, added ([5e16f95](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/5e16f957fdff18d57e87b3afee84fd543d067981))
* icon url removed in message attachment ([b4bc862](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/b4bc862a73f0a1925fbd3f9a3a732deb56dd18ae))
* Mattermost text output ([abab34f](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/abab34f3e6f7c3f23474ce224471beefea7c17e9))


### Features

* added username in output message ([0343de2](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/0343de230db574cd845078021ef6ab83336d4b42))
* can override check name by adding alias key value in check annotation ([4f1964e](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/4f1964e95c4c3a784480112a689dcf8c0feb7482))
* code updated to frame json output for type application ([604172f](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/604172f9026aa9dc27406990b3e6dffcac2bb9f6))
* insecure certificate post added ([f01c3ff](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/f01c3ff966b4711efb1c25194a80255e7c1d8c75))
* monitor node alert standard added ([8e56772](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/8e56772b83eed3a2f952c69a5cb731729d68bb35))
* now check names support regex ([5504209](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/5504209f4052a0d65b3bf57814ec119babb515d4))

# [1.0.0](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/compare/...1.0.0) (2022-12-27)


### Features

* webhook notify handler golang code added ([7dbb60c](https://git.heimdall.mydbops.com/mydb/devops/sensu-plugins/mydbops-sensu-slack-handler/commit/7dbb60c118a78b74297e63190344b89defa48afd))
