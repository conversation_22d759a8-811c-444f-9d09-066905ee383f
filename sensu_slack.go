package main

import (
	"bytes"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	odjson "gitlab.com/c0b/go-ordered-json"

	corev2 "github.com/sensu/sensu-go/api/core/v2"
	"github.com/sensu/sensu-plugin-sdk/sensu"
	"github.com/sensu/sensu-plugin-sdk/templates"
)

type WebHook struct {
	hookURL string
}

type AttachmentField struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}

type Attachment struct {
	Color    string `json:"color,omitempty"`
	Fallback string `json:"fallback"`

	AuthorName    string `json:"author_name,omitempty"`
	AuthorSubname string `json:"author_subname,omitempty"`
	AuthorLink    string `json:"author_link,omitempty"`
	AuthorIcon    string `json:"author_icon,omitempty"`

	Title     string `json:"title,omitempty"`
	TitleLink string `json:"title_link,omitempty"`
	Pretext   string `json:"pretext,omitempty"`
	Text      string `json:"text"`

	ImageURL string `json:"image_url,omitempty"`
	ThumbURL string `json:"thumb_url,omitempty"`

	Footer     string `json:"footer,omitempty"`
	FooterIcon string `json:"footer_icon,omitempty"`
	TimeStamp  int64  `json:"ts,omitempty"`

	Fields     []*AttachmentField `json:"fields,omitempty"`
	MarkdownIn []string           `json:"mrkdwn_in,omitempty"`
}

type WebHookPostPayload struct {
	Text     string `json:"text,omitempty"`
	Channel  string `json:"channel,omitempty"`
	Username string `json:"username,omitempty"`
	// Attachments []*Attachment `json:"attachments,omitempty"`
}

// HandlerConfig contains the Slack handler configuration
type HandlerConfig struct {
	sensu.PluginConfig
	mydbopsRegexSplit         []string
	mydbopsCheckNames         []string
	slackwebHookURL           []string
	slackChannel              []string
	enableMydbMonitorStandard string
	sensuApiKeyForMonitornode string
	reportSlackChannel        string
	reportSlackWebHook        string
	slackUsername             string
	slackIconURL              string
	slackOutputFormat         string
	slackJsonPretty           bool
	applicationOutputTemplate bool
	slackDescriptionTemplate  string
	status                    []string
}

type CheckFormat struct {
	Type         string      `json:"type"`
	CheckName    string      `json:"check_name"`
	HostName     string      `json:"host_name"`
	ClientName   string      `json:"client_name"`
	ExecutedTime int64       `json:"executed_time"`
	Duration     float64     `json:"duration"`
	ExitStatus   uint32      `json:"exit_status"`
	Message      string      `json:"message"`
	Output       interface{} `json:"output"`
	NotifyBy     string      `json:"notify_by"`
}

type AppFormat struct {
	Type       string      `json:"type"`
	Name       string      `json:"name"`
	HostName   string      `json:"host_name"`
	ClientName string      `json:"client_name"`
	StartTime  string      `json:"startTime"`
	EndTime    string      `json:"endTime"`
	Duration   float64     `json:"duration"`
	LogLevel   string      `json:"logLevel"`
	Message    string      `json:"message"`
	Output     interface{} `json:"output"`
	NotifyBy   string      `json:"notify_by"`
}

const (
	regexSplit          = "regex-entity"
	checkNames          = "check-names"
	mydbMonStandard     = "mydbmon-match-alert"
	apiKey              = "sensu-apikey"
	reportSlackChannel  = "report-channel"
	reportSlackWebHook  = "report-webhook"
	webHookURL          = "webhook-url"
	channel             = "channel"
	username            = "username"
	iconURL             = "icon-url"
	format              = "format"
	pretty              = "pretty"
	appOutTemplate      = "application"
	descriptionTemplate = "description-template"
	acceptStatus        = "acceptedStatus"

	defaultFormat        = "text"
	defaultPretty        = false
	defaultIconURL       = "https://www.sensu.io/img/sensu-logo.png"
	defaultUsername      = "sensu"
	defaultTemplate      = "{{ .Check.Output }}"
	defaultHttpProxyURL  = ""
	defaultHttpsProxyURL = ""
)

var (
	config = HandlerConfig{
		PluginConfig: sensu.PluginConfig{
			Name:     "sensu-slack-handler",
			Short:    "The Sensu Go Slack handler for notifying a channel",
			Keyspace: "sensu.io/plugins/slack/config",
		},
	}

	slackConfigOptions = []*sensu.PluginConfigOption{
		{
			Path:      webHookURL,
			Argument:  webHookURL,
			Shorthand: "w",
			Secret:    true,
			Usage:     "The webhook url to send messages to, can call multiple times with channel and regex flag",
			Value:     &config.slackwebHookURL,
		},
		{
			Path:     reportSlackWebHook,
			Argument: reportSlackWebHook,
			Secret:   true,
			Usage:    "The report webhook url to send regex missing messages to",
			Value:    &config.reportSlackWebHook,
		},
		{
			Path:      mydbMonStandard,
			Argument:  mydbMonStandard,
			Shorthand: "e",
			Usage:     "To enable mydbops monitor node alert standard, This comma separated values will match with specified regex and sensu entity lists. then sent notifications to all matched webhooks, example: mysql,mongo,postgres,mariadb,proxysql",
			Value:     &config.enableMydbMonitorStandard,
		},
		{
			Path:      apiKey,
			Argument:  apiKey,
			Shorthand: "k",
			Usage:     "sensu api key used to get entity lists, required only if mydbmon-match-alert flag called",
			Value:     &config.sensuApiKeyForMonitornode,
		},
		{
			Path:      regexSplit,
			Argument:  regexSplit,
			Shorthand: "r",
			Usage:     "send specific alerts to specific channel based on regex-entity, can call multiple times with channel and webhook flag. example --regex-entity '.*' or '_mysql|_mariadb'",
			Value:     &config.mydbopsRegexSplit,
		},
		{
			Path:      checkNames,
			Argument:  checkNames,
			Shorthand: "s",
			Usage:     "send specific alerts to specific channel based on check names regex specified, can call multiple times with channel and webhook flag. example 'Check_CPU|Check_Memory'",
			Value:     &config.mydbopsCheckNames,
		},
		{
			Path:      channel,
			Env:       "SLACK_CHANNEL",
			Argument:  channel,
			Shorthand: "c",
			Usage:     "The channel to post messages to, can call multiple times with webhook and regex flag",
			Value:     &config.slackChannel,
		},
		{
			Path:     reportSlackChannel,
			Argument: reportSlackChannel,
			Usage:    "The report channel to send regex missing messages to",
			Value:    &config.reportSlackChannel,
		},
		{
			Path:     acceptStatus,
			Argument: acceptStatus,
			Usage:    "Allowed status in integer(eg: 1|2|3) (accepts pipe delimited and/or multiple flags)",
			Value:    &config.status,
		},
		{
			Path:      username,
			Env:       "SLACK_USERNAME",
			Argument:  username,
			Shorthand: "u",
			Default:   defaultUsername,
			Usage:     "The username that messages will be sent as",
			Value:     &config.slackUsername,
		},
		{
			Path:      iconURL,
			Env:       "SLACK_ICON_URL",
			Argument:  iconURL,
			Shorthand: "i",
			Default:   defaultIconURL,
			Usage:     "A URL to an image to use as the user avatar",
			Value:     &config.slackIconURL,
		},
		{
			Path:      format,
			Argument:  format,
			Shorthand: "f",
			Default:   defaultFormat,
			Usage:     "Slack Output Message Formar(Available options: json, text)",
			Value:     &config.slackOutputFormat,
		},
		{
			Path:     pretty,
			Argument: pretty,
			Default:  defaultPretty,
			Usage:    "Enable Slack Output Json Pretty Printer",
			Value:    &config.slackJsonPretty,
		},
		{
			Path:     appOutTemplate,
			Argument: appOutTemplate,
			Usage:    "Enable Slack Output Application template only works with json format",
			Value:    &config.applicationOutputTemplate,
		},
		{
			Path:      descriptionTemplate,
			Env:       "SLACK_DESCRIPTION_TEMPLATE",
			Argument:  descriptionTemplate,
			Shorthand: "t",
			Default:   defaultTemplate,
			Usage:     "The Slack notification output template, in Golang text/template format",
			Value:     &config.slackDescriptionTemplate,
		},
	}
)

func main() {
	goHandler := sensu.NewGoHandler(&config.PluginConfig, slackConfigOptions, checkArgs, sendMessage)
	goHandler.Execute()
}

func checkArgs(_ *corev2.Event) error {
	// Support deprecated environment variables

	if (config.enableMydbMonitorStandard == "" && config.sensuApiKeyForMonitornode != "") || (config.enableMydbMonitorStandard != "" && config.sensuApiKeyForMonitornode == "") {
		return fmt.Errorf("mydbmon-match-alert and sensu-apikey options should call equally")
	}

	if len(config.mydbopsRegexSplit) != len(config.slackwebHookURL) {
		return fmt.Errorf("array value mismatched 'webhook-url, channel, check-names and regex-entity' options should call equally")
	}

	if len(config.slackwebHookURL) != len(config.slackChannel) {
		return fmt.Errorf("array value mismatched 'webhook-url, channel, check-names and regex-entity' options should call equally")
	}

	if len(config.slackChannel) != len(config.mydbopsCheckNames) {
		return fmt.Errorf("array value mismatched 'webhook-url, channel, check-names and regex-entity' options should call equally")
	}

	if username := os.Getenv("SENSU_SLACK_USERNAME"); username != "" && config.slackUsername == defaultUsername {
		config.slackUsername = username
	}
	if icon := os.Getenv("SENSU_SLACK_ICON_URL"); icon != "" && config.slackIconURL == defaultIconURL {
		config.slackIconURL = icon
	}

	if len(config.slackwebHookURL) == 0 {
		return fmt.Errorf("--%s or SLACK_WEBHOOK_URL environment variable is required", webHookURL)
	}

	return nil
}

func NewWebHook(hookURL string) *WebHook {
	return &WebHook{hookURL}
}

func (hk *WebHook) PostMessage(payload *WebHookPostPayload) error {

	body, err := json.Marshal(payload)
	if err != nil {
		return err
	}
	req, err := http.NewRequest("POST", hk.hookURL, bytes.NewReader(body))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	proxyUrlEnables := false
	mydb_http_proxy := os.Getenv("mydb_http_proxy")
	if mydb_http_proxy != "" && strings.HasPrefix(hk.hookURL, "http") {
		proxyURL, err := url.Parse(mydb_http_proxy)
		if err != nil {
			return fmt.Errorf("http proxy url parsing error %v", err)
		}
		if proxyURL != nil {
			tr.Proxy = http.ProxyURL(proxyURL)
			proxyUrlEnables = true
		}
	}

	mydb_https_proxy := os.Getenv("mydb_https_proxy")
	if mydb_https_proxy != "" && strings.HasPrefix(hk.hookURL, "https") {
		proxyURL, err := url.Parse(mydb_https_proxy)
		if err != nil {
			return fmt.Errorf("https proxy url parsing error %v", err)
		}
		if proxyURL != nil {
			tr.Proxy = http.ProxyURL(proxyURL)
			proxyUrlEnables = true
		}
	}

	mydbproxy_user := os.Getenv("mydb_proxy_user")
	mydbproxy_pass := os.Getenv("mydb_proxy_pass")
	if mydbproxy_user != "" && mydbproxy_pass != "" && proxyUrlEnables {
		auth := mydbproxy_user + ":" + mydbproxy_pass
		basicAuth := "Basic " + base64.StdEncoding.EncodeToString([]byte(auth))
		hdr := http.Header{}
		hdr.Add("Proxy-Authorization", basicAuth)
		tr.ProxyConnectHeader = hdr
	}

	client := http.Client{Transport: tr}

	const maxAttempts = 5
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		// create a fresh request each attempt (body reader is consumed)
		req, err := http.NewRequest("POST", hk.hookURL, bytes.NewReader(body))
		if err != nil {
			return err
		}
		req.Header.Set("Content-Type", "application/json")

		resp, err := client.Do(req)
		if err != nil {
			return err
		}

		// success
		if resp.StatusCode == http.StatusOK {
			resp.Body.Close()
			return nil
		}

		// rate limited
		if resp.StatusCode == http.StatusTooManyRequests {
			delay := retryAfterDuration(resp.Header.Get("Retry-After"))
			resp.Body.Close()
			if attempt == maxAttempts {
				return fmt.Errorf("slack rate limit (429): exhausted retries after %d attempts", attempt)
			}
			time.Sleep(delay)
			continue
		}

		// transient server errors -> simple linear backoff
		if resp.StatusCode >= 500 && resp.StatusCode < 600 {
			resp.Body.Close()
			if attempt == maxAttempts {
				return fmt.Errorf("slack server error %d: exhausted retries after %d attempts", resp.StatusCode, attempt)
			}
			time.Sleep(time.Duration(attempt) * time.Second)
			continue
		}

		// non-retriable
		t, _ := ioutil.ReadAll(resp.Body)
		resp.Body.Close()
		return fmt.Errorf(string(t))
	}

	// should not reach here
	return fmt.Errorf("unexpected exit from retry loop")

}

func frameJsonAppMsgFromPlainText(event *corev2.Event, message string) (string, error) {
	var level string
	if event.Check.Status != 0 {
		level = "E"
	} else {
		level = "S"
	}
	endDuration := event.Check.Executed + int64(event.Check.Duration)
	attachment := AppFormat{
		Type:       "Application",
		Name:       event.Check.Name,
		HostName:   event.Entity.Name,
		ClientName: config.slackUsername,
		StartTime:  time.Unix(event.Check.Executed, 0).Format("2006-01-02 15:04:05"),
		EndTime:    time.Unix(endDuration, 0).Format("2006-01-02 15:04:05"),
		Duration:   event.Check.Duration,
		LogLevel:   level,
		Message:    message,
		NotifyBy:   "sensu-handler",
	}
	var (
		data []byte
		err  error
	)
	if config.slackJsonPretty {
		data, err = json.MarshalIndent(attachment, "", "\t")
	} else {
		data, err = json.Marshal(attachment)
	}
	if err != nil {
		return "", err
	}
	return string(data), nil
}

func frameJsonCheckMsgFromPlainText(event *corev2.Event, message string) (string, error) {
	attachment := CheckFormat{
		Type:         "Checks",
		CheckName:    event.Check.Name,
		HostName:     event.Entity.Name,
		ClientName:   config.slackUsername,
		ExecutedTime: event.Check.Executed,
		Duration:     event.Check.Duration,
		ExitStatus:   event.Check.Status,
		Message:      message,
		NotifyBy:     "sensu-handler",
	}
	var (
		data []byte
		err  error
	)
	if config.slackJsonPretty {
		data, err = json.MarshalIndent(attachment, "", "\t")
	} else {
		data, err = json.Marshal(attachment)
	}
	if err != nil {
		return "", err
	}
	return string(data), nil
}

func addStandardFields(format string, event *corev2.Event, message string) (string, error) {
	var retriveData *odjson.OrderedMap = odjson.NewOrderedMap()
	err := json.Unmarshal([]byte(message), retriveData)
	if err != nil {
		return "", err
	}
	alertType := retriveData.Get("type")
	var Type string
	if alertType == nil {
		Type = ""
	} else {
		Type = fmt.Sprintf("%v", alertType)
	}
	var newData interface{}

	if format == "json" || config.applicationOutputTemplate {
		if Type == "Checks" || Type == "Metrics" || (Type == "" && !config.applicationOutputTemplate) {
			var Data CheckFormat
			err := json.Unmarshal([]byte(message), &Data)
			if err != nil {
				return "", err
			}
			Data.CheckName = event.Check.Name
			Data.HostName = event.Entity.Name
			Data.ClientName = config.slackUsername
			Data.ExecutedTime = event.Check.Executed
			Data.Duration = event.Check.Duration
			Data.ExitStatus = event.Check.Status
			Data.NotifyBy = "sensu-handler"
			if Type == "" {
				Data.Type = "Checks"
			}
			newData = Data
		} else {
			if Type == "" && config.applicationOutputTemplate {
				retriveData.Set("type", "Application")
			}
			retriveData.Set("client_name", config.slackUsername)
			retriveData.Set("host_name", event.Entity.Name)
			retriveData.Set("notify_by", "sensu-handler")
			newData = retriveData
		}
		var (
			data []byte
		)
		if config.slackJsonPretty {
			data, err = json.MarshalIndent(newData, "", "\t")
		} else {
			data, err = json.Marshal(newData)
		}
		if err != nil {
			return "", err
		}
		return string(data), nil
	} else {
		icon := messageIcon(event)
		return fmt.Sprintf("%s USER: %s HOST: %s SERVICE: %s MESSAGE: %v", icon, config.slackUsername, event.Entity.Name, event.Check.Name, retriveData.Get("message")), nil
	}
}

func messageIcon(event *corev2.Event) string {
	switch event.Check.Status {
	case 0:
		return ":white_check_mark:"
	case 1:
		return ":warning:"
	case 2:
		return ":exclamation:"
	case 3:
		return ":question:"
	default:
		return ":white_medium_square:"
	}
}

func isJSON(s string) bool {
	var js map[string]interface{}
	return json.Unmarshal([]byte(s), &js) == nil
}

func messageAttachment(event *corev2.Event) *Attachment {
	var (
		outputmessage string
	)
	icon := messageIcon(event)
	description, err := templates.EvalTemplate("description", config.slackDescriptionTemplate, event)
	if err != nil {
		outputmessage = fmt.Sprintf("%s: Error processing template: %s", config.PluginConfig.Name, err)
	} else {
		description = strings.Replace(description, `\n`, "\n", -1)
		checkName, ok := event.Check.Annotations["alias"]
		if ok {
			event.Check.Name = checkName
		}
		entityName, entityOk := event.Entity.Annotations["entity_alias"]
		if entityOk {
			event.Entity.Name = entityName
		}
		if isJSON(description) {
			outputmessage, err = addStandardFields(config.slackOutputFormat, event, description)
			if err != nil {
				outputmessage = fmt.Sprintf("%s", err)
			}
		} else {
			if config.applicationOutputTemplate {
				outputmessage, err = frameJsonAppMsgFromPlainText(event, description)
				if err != nil {
					outputmessage = fmt.Sprintf("%s", err)
				}
			} else if config.slackOutputFormat == "json" {
				outputmessage, err = frameJsonCheckMsgFromPlainText(event, description)
				if err != nil {
					outputmessage = fmt.Sprintf("%s", err)
				}
			} else {
				outputmessage = fmt.Sprintf("%s USER: %s HOST: %s SERVICE: %s MESSAGE: %s", icon, config.slackUsername, event.Entity.Name, event.Check.Name, description)
			}
		}
	}
	attachment := &Attachment{
		Text: outputmessage,
	}
	return attachment
}

func triggerApi(url string, chann string, event *corev2.Event) error {
	hook := NewWebHook(url)
	// var outMessage []string
	// var err error
	// if (event.Check.Name == "Check_Disk") && (strings.Contains(event.Check.Output, "Critical") && strings.Contains(event.Check.Output, "Warning")) {
	// 	// Regular expression to capture both patterns
	// 	re := regexp.MustCompile(`(Critical\[(.*?)\]:\s*([^W]+)\s*Warning:\s*(.*))|(Warning\[(.*?)\]:\s*([^W]+)\s*Critical:\s*(.*))`)
	// 	// Find the matches
	// 	matches := re.FindStringSubmatch(event.Check.Output)
	// 	if len(matches) != 0 {
	// 		// Extract the Critical and Warning parts
	// 		if matches[1] != "" {
	// 			// Case where Critical comes first, followed by Warning
	// 			threshold := matches[2]       // Content inside Critical[...]
	// 			criticalDetails := matches[3] // Details after Critical[...] and
	// 			warningDetails := matches[4]  // Details after Warning[...] and ":"

	// 			// Print the results
	// 			outMessage = append(outMessage, fmt.Sprintf("Critical[%s]: %s\n", threshold, criticalDetails))
	// 			outMessage = append(outMessage, fmt.Sprintf("Warning[%s]: %s\n", threshold, warningDetails))

	// 		} else if matches[5] != "" {
	// 			// Case where Warning comes first, followed by Critical
	// 			threshold := matches[6]       // Content inside Warning[...]
	// 			criticalDetails := matches[7] // Details after Warning[...] and
	// 			warningDetails := matches[8]  // Details after Critical[...] and ":"

	// 			// Store the results
	// 			outMessage = append(outMessage, fmt.Sprintf("Critical[%s]: %s\n", threshold, criticalDetails))
	// 			outMessage = append(outMessage, fmt.Sprintf("Warning[%s]: %s\n", threshold, warningDetails))
	// 		}

	// 		for i := range outMessage {
	// 			event.Check.Output = outMessage[i]
	// 			//setting the status for event (used for icon in slack)
	// 			if strings.Contains(outMessage[i], "Critical") {
	// 				event.Check.Status = 2
	// 			} else if strings.Contains(outMessage[i], "Warning") {
	// 				event.Check.Status = 1
	// 			}
	// 			err = hook.PostMessage(&WebHookPostPayload{
	// 				//		Attachments: []*Attachment{messageAttachment(event)},
	// 				Text:     messageAttachment(event).Text,
	// 				Channel:  chann,
	// 				Username: config.slackUsername,
	// 			})
	// 		}
	// 	} else {
	// 		//if the regex is not matched the message will be sent as usual
	// 		err = hook.PostMessage(&WebHookPostPayload{
	// 			//		Attachments: []*Attachment{messageAttachment(event)},
	// 			Text:     messageAttachment(event).Text,
	// 			Channel:  chann,
	// 			Username: config.slackUsername,
	// 		})
	// 	}

	// } else {
	//if the check name is not check_disk and it does not contain critical and warning then it will be sent as usual
	err := hook.PostMessage(&WebHookPostPayload{
		//		Attachments: []*Attachment{messageAttachment(event)},
		Text:     messageAttachment(event).Text,
		Channel:  chann,
		Username: config.slackUsername,
	})
	// }
	if err != nil {
		return fmt.Errorf("failed to send slack message: %v", err)
	}
	return nil
}

type entityName struct {
	Metadata struct {
		Name string `json:"name"`
	} `json:"metadata"`
}

func checkMonitorNodeEvents() ([]string, error) {
	//	monitor_allow := `mysql,mongo,postgres,proxysql,mariadb`
	url := `https://127.0.0.1:8080/api/core/v2/namespaces/default/entities`
	//	key := `10c9c5ee-7906-4d48-96e0-6a75c7286a18`
	req, err := http.NewRequest("GET", url, bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Key "+config.sensuApiKeyForMonitornode)
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	client := http.Client{Transport: tr}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	var entityLists []entityName
	err = json.NewDecoder(resp.Body).Decode(&entityLists)
	if err != nil {
		return nil, err
	}
	allowedList := strings.Split(config.enableMydbMonitorStandard, ",")
	fmt.Println(allowedList)
	var filter []string
	for _, entity := range entityLists {
		for _, v := range allowedList {
			val := strings.TrimSpace(v)
			if strings.Contains(entity.Metadata.Name, val) {
				filter = append(filter, val)
				//				allowedList = append(allowedList[:k], allowedList[k+1:]...)
			}
		}
	}
	return filter, nil
}

func unique(slice []string) []string {
	uniqMap := make(map[string]struct{})
	for _, v := range slice {
		uniqMap[v] = struct{}{}
	}

	uniqSlice := make([]string, 0, len(uniqMap))
	for v := range uniqMap {
		uniqSlice = append(uniqSlice, v)
	}
	return uniqSlice
}

func monitorNodeFilterCheck(filters []string, reg string) bool {
	for _, v := range filters {
		if strings.Contains(reg, v) {
			return true
		}
	}
	return false
}

func isExistinSlice(arr []string, target string) bool {
	for _, v := range arr {
		if strings.TrimSpace(v) == target {
			return true
		}
	}
	return false
}

func sendMessage(event *corev2.Event) error {
	var (
		sent    bool
		filters []string
	)
	if config.enableMydbMonitorStandard != "" && strings.Contains(event.Entity.Name, "_monitor") {
		filtered, err := checkMonitorNodeEvents()
		if err != nil {
			return err
		}
		filters = unique(filtered)
	}
	for k, reg := range config.mydbopsRegexSplit {
		r, err := regexp.Compile(reg)
		if err != nil {
			return fmt.Errorf("failed to compile given entity regex(%s): %v", reg, err)
		}
		monitorNodeMatch := monitorNodeFilterCheck(filters, reg)
		entityMatch := r.MatchString(event.Entity.Name)
		checkReg, err := regexp.Compile(config.mydbopsCheckNames[k])
		if err != nil {
			return fmt.Errorf("failed to compile given check regex(%s): %v", config.mydbopsCheckNames[k], err)
		}
		checkMatch := checkReg.MatchString(event.Check.Name)
		var statusMatch bool
		if k < len(config.status) {
			status := strings.Split(config.status[k], "|")
			statusMatch = isExistinSlice(status, fmt.Sprint(event.Check.Status))
		} else {
			statusMatch = true
		}
		if (entityMatch && checkMatch && statusMatch) || monitorNodeMatch {
			err := triggerApi(config.slackwebHookURL[k], config.slackChannel[k], event)
			if err != nil {
				return err
			}
			fmt.Printf("Notification sent to Slack channel %s\n", config.slackChannel[k])
			sent = true
		}
		checkMatch = false
	}
	if !sent && config.reportSlackWebHook != "" && config.reportSlackChannel != "" {
		err := triggerApi(config.reportSlackWebHook, config.reportSlackChannel, event)
		if err != nil {
			return err
		}
		fmt.Printf("Notification sent to Slack channel %s\n", config.reportSlackChannel)
	}
	return nil
}

// retryAfterDuration returns the duration to wait before retrying a failed request.
func retryAfterDuration(h string) time.Duration {
	if h == "" {
		return time.Second
	}
	if secs, err := strconv.Atoi(h); err == nil {
		if secs < 1 {
			secs = 1
		}
		return time.Duration(secs) * time.Second
	}
	if t, err := http.ParseTime(h); err == nil {
		d := time.Until(t)
		if d < time.Second {
			d = time.Second
		}
		return d
	}
	return time.Second
}
